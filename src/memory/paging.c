/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   paging.c                                           :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/* Global page directories */
t_page_directory *kernel_directory = 0;
t_page_directory *current_directory = 0;

/* Temporary page table for initial mapping */
static t_page_table *temp_page_tables[1024];

/**
 * Enable paging by setting the PG bit in CR0
 */
void paging_enable(void)
{
    uint32_t cr0;
    
    __asm__ volatile("mov %%cr0, %0" : "=r"(cr0));
    cr0 |= 0x80000000; /* Set PG bit */
    __asm__ volatile("mov %0, %%cr0" :: "r"(cr0));
}

/**
 * Disable paging by clearing the PG bit in CR0
 */
void paging_disable(void)
{
    uint32_t cr0;
    
    __asm__ volatile("mov %%cr0, %0" : "=r"(cr0));
    cr0 &= ~0x80000000; /* Clear PG bit */
    __asm__ volatile("mov %0, %%cr0" :: "r"(cr0));
}

/**
 * Switch to a different page directory
 * @param dir Page directory to switch to
 */
void paging_switch_directory(t_page_directory *dir)
{
    current_directory = dir;
    __asm__ volatile("mov %0, %%cr3" :: "r"(dir->physical_addr));
}

/**
 * Get the page table for a virtual address
 * @param virt_addr Virtual address
 * @param create Whether to create the page table if it doesn't exist
 * @return Pointer to page table, or NULL if not found and create is 0
 */
t_page_table* paging_get_page_table(uint32_t virt_addr, int create)
{
    uint32_t dir_index = PAGE_DIR_INDEX(virt_addr);
    t_page_dir_entry *dir_entry = &current_directory->entries[dir_index];
    
    /* Check if page table exists */
    if (!dir_entry->present) {
        if (!create) {
            return NULL;
        }
        
        /* Allocate a new page table */
        uint32_t phys_addr = phys_alloc_frame();
        if (phys_addr == 0) {
            PANIC("Failed to allocate frame for page table");
            return NULL;
        }
        
        /* For now, we'll use a simple identity mapping for page tables */
        t_page_table *table = (t_page_table*)phys_addr;
        
        /* Clear the page table */
        for (int i = 0; i < PAGE_ENTRIES; i++) {
            table->entries[i].present = 0;
            table->entries[i].writable = 0;
            table->entries[i].user = 0;
            table->entries[i].frame = 0;
        }
        
        /* Set up directory entry */
        dir_entry->present = 1;
        dir_entry->writable = 1;
        dir_entry->user = 0;
        dir_entry->frame = phys_addr >> 12;
        
        temp_page_tables[dir_index] = table;
        return table;
    }
    
    /* Page table exists, return it */
    if (temp_page_tables[dir_index]) {
        return temp_page_tables[dir_index];
    }
    
    /* This is a simplified approach - in a full implementation,
       we'd need proper virtual memory mapping for page tables */
    return (t_page_table*)(dir_entry->frame << 12);
}

/**
 * Map a virtual address to a physical address
 * @param virt_addr Virtual address to map
 * @param phys_addr Physical address to map to
 * @param flags Page flags
 * @return 0 on success, -1 on failure
 */
int paging_map_page(uint32_t virt_addr, uint32_t phys_addr, uint32_t flags)
{
    uint32_t page_index = PAGE_INDEX(virt_addr);
    t_page_table *table;
    t_page_table_entry *entry;
    
    /* Align addresses */
    virt_addr = PAGE_ALIGN(virt_addr);
    phys_addr = PAGE_ALIGN(phys_addr);
    
    /* Get or create page table */
    table = paging_get_page_table(virt_addr, 1);
    if (!table) {
        return -1;
    }
    
    entry = &table->entries[page_index];
    
    /* Check if page is already mapped */
    if (entry->present) {
        WARN("Attempted to map already mapped page");
        return -1;
    }
    
    /* Set up page table entry */
    entry->present = (flags & PAGE_PRESENT) ? 1 : 0;
    entry->writable = (flags & PAGE_WRITABLE) ? 1 : 0;
    entry->user = (flags & PAGE_USER) ? 1 : 0;
    entry->pwt = (flags & PAGE_WRITE_THROUGH) ? 1 : 0;
    entry->pcd = (flags & PAGE_CACHE_DISABLE) ? 1 : 0;
    entry->global = (flags & PAGE_GLOBAL) ? 1 : 0;
    entry->frame = phys_addr >> 12;
    
    /* Invalidate TLB entry */
    __asm__ volatile("invlpg (%0)" :: "r"(virt_addr) : "memory");
    
    return 0;
}

/**
 * Unmap a virtual address
 * @param virt_addr Virtual address to unmap
 */
void paging_unmap_page(uint32_t virt_addr)
{
    uint32_t page_index = PAGE_INDEX(virt_addr);
    t_page_table *table;
    t_page_table_entry *entry;
    
    virt_addr = PAGE_ALIGN(virt_addr);
    
    table = paging_get_page_table(virt_addr, 0);
    if (!table) {
        WARN("Attempted to unmap page from non-existent page table");
        return;
    }
    
    entry = &table->entries[page_index];
    
    if (!entry->present) {
        WARN("Attempted to unmap non-present page");
        return;
    }
    
    /* Free the physical frame */
    phys_free_frame(entry->frame << 12);
    
    /* Clear the page table entry */
    entry->present = 0;
    entry->writable = 0;
    entry->user = 0;
    entry->frame = 0;
    
    /* Invalidate TLB entry */
    __asm__ volatile("invlpg (%0)" :: "r"(virt_addr) : "memory");
}

/**
 * Get the physical address for a virtual address
 * @param virt_addr Virtual address
 * @return Physical address, or 0 if not mapped
 */
uint32_t paging_get_physical_addr(uint32_t virt_addr)
{
    uint32_t page_index = PAGE_INDEX(virt_addr);
    uint32_t offset = PAGE_OFFSET(virt_addr);
    t_page_table *table;
    t_page_table_entry *entry;
    
    table = paging_get_page_table(virt_addr, 0);
    if (!table) {
        return 0;
    }
    
    entry = &table->entries[page_index];
    if (!entry->present) {
        return 0;
    }
    
    return (entry->frame << 12) + offset;
}

/**
 * Create a new page directory
 * @return Pointer to new page directory, or NULL on failure
 */
t_page_directory* paging_create_directory(void)
{
    uint32_t phys_addr;
    t_page_directory *dir;
    int i;
    
    /* Allocate physical frame for directory */
    phys_addr = phys_alloc_frame();
    if (phys_addr == 0) {
        PANIC("Failed to allocate frame for page directory");
        return NULL;
    }
    
    /* For now, use identity mapping */
    dir = (t_page_directory*)phys_addr;
    dir->physical_addr = phys_addr;
    
    /* Initialize all entries as not present */
    for (i = 0; i < PAGE_ENTRIES; i++) {
        dir->entries[i].present = 0;
        dir->entries[i].writable = 0;
        dir->entries[i].user = 0;
        dir->entries[i].frame = 0;
    }
    
    return dir;
}

/**
 * Destroy a page directory and all its page tables
 * @param dir Page directory to destroy
 */
void paging_destroy_directory(t_page_directory *dir)
{
    int i;
    
    if (!dir || dir == kernel_directory) {
        WARN("Attempted to destroy kernel directory or NULL directory");
        return;
    }
    
    /* Free all page tables */
    for (i = 0; i < PAGE_ENTRIES; i++) {
        if (dir->entries[i].present) {
            phys_free_frame(dir->entries[i].frame << 12);
        }
    }
    
    /* Free the directory itself */
    phys_free_frame(dir->physical_addr);
}

/**
 * Initialize paging system
 */
void paging_init(void)
{
    uint32_t i;
    
    terminal_writestring("Initializing paging system...\n");
    
    /* Create kernel page directory */
    kernel_directory = paging_create_directory();
    if (!kernel_directory) {
        PANIC("Failed to create kernel page directory");
        return;
    }
    
    /* Identity map the first 4MB (kernel space) */
    for (i = 0; i < 0x400000; i += PAGE_SIZE) {
        paging_map_page(i, i, PAGE_PRESENT | PAGE_WRITABLE);
    }
    
    /* Map VGA memory */
    paging_map_page(VGA_MEMORY, VGA_MEMORY, PAGE_PRESENT | PAGE_WRITABLE);
    
    /* Switch to kernel directory */
    current_directory = kernel_directory;
    paging_switch_directory(kernel_directory);
    
    /* Enable paging */
    paging_enable();
    
    terminal_writestring("Paging enabled successfully!\n");
}
