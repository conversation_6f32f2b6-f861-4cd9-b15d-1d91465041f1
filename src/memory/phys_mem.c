/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   phys_mem.c                                         :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/* Global physical memory manager */
t_phys_mem_manager phys_mem_mgr = {0};

/* Bitmap for tracking physical frames */
static uint32_t phys_bitmap[32768]; /* Support up to 4GB of RAM (32768 * 32 * 4KB) */

/* External symbols from linker script */
extern uint32_t kernel_end;

/**
 * Initialize the physical memory manager
 * @param mem_size Total memory size in bytes
 */
void phys_mem_init(uint32_t mem_size)
{
    uint32_t i;
    uint32_t kernel_end_addr;
    uint32_t bitmap_size;
    
    /* Calculate total number of frames */
    phys_mem_mgr.total_frames = mem_size / PAGE_SIZE;
    phys_mem_mgr.used_frames = 0;
    phys_mem_mgr.free_frames = phys_mem_mgr.total_frames;
    phys_mem_mgr.first_free_frame = 0;
    
    /* Set bitmap pointer */
    phys_mem_mgr.bitmap = phys_bitmap;
    
    /* Calculate bitmap size in bytes */
    bitmap_size = (phys_mem_mgr.total_frames + 31) / 32 * sizeof(uint32_t);
    
    /* Initialize bitmap - all frames are free initially */
    for (i = 0; i < bitmap_size / sizeof(uint32_t); i++) {
        phys_mem_mgr.bitmap[i] = 0;
    }
    
    /* Mark frames used by kernel as occupied */
    kernel_end_addr = (uint32_t)&kernel_end;
    
    /* Mark frames from 0 to kernel_end as used */
    for (i = 0; i < kernel_end_addr; i += PAGE_SIZE) {
        phys_set_frame(i);
    }
    
    terminal_writestring("Physical memory manager initialized\n");
    terminal_writestring("Total frames: ");
    printnbr(phys_mem_mgr.total_frames, 10);
    terminal_writestring("\nUsed frames: ");
    printnbr(phys_mem_mgr.used_frames, 10);
    terminal_writestring("\nFree frames: ");
    printnbr(phys_mem_mgr.free_frames, 10);
    terminal_writestring("\n");
}

/**
 * Set a frame as used in the bitmap
 * @param frame_addr Physical address of the frame
 */
void phys_set_frame(uint32_t frame_addr)
{
    uint32_t frame = frame_addr / PAGE_SIZE;
    uint32_t idx = frame / 32;
    uint32_t off = frame % 32;
    
    if (frame >= phys_mem_mgr.total_frames)
        return;
    
    /* If frame was free, mark it as used */
    if (!(phys_mem_mgr.bitmap[idx] & (1 << off))) {
        phys_mem_mgr.bitmap[idx] |= (1 << off);
        phys_mem_mgr.used_frames++;
        phys_mem_mgr.free_frames--;
    }
}

/**
 * Clear a frame as free in the bitmap
 * @param frame_addr Physical address of the frame
 */
void phys_clear_frame(uint32_t frame_addr)
{
    uint32_t frame = frame_addr / PAGE_SIZE;
    uint32_t idx = frame / 32;
    uint32_t off = frame % 32;
    
    if (frame >= phys_mem_mgr.total_frames)
        return;
    
    /* If frame was used, mark it as free */
    if (phys_mem_mgr.bitmap[idx] & (1 << off)) {
        phys_mem_mgr.bitmap[idx] &= ~(1 << off);
        phys_mem_mgr.used_frames--;
        phys_mem_mgr.free_frames++;
        
        /* Update first free frame hint */
        if (frame < phys_mem_mgr.first_free_frame) {
            phys_mem_mgr.first_free_frame = frame;
        }
    }
}

/**
 * Test if a frame is set (used)
 * @param frame_addr Physical address of the frame
 * @return 1 if frame is used, 0 if free
 */
int phys_test_frame(uint32_t frame_addr)
{
    uint32_t frame = frame_addr / PAGE_SIZE;
    uint32_t idx = frame / 32;
    uint32_t off = frame % 32;
    
    if (frame >= phys_mem_mgr.total_frames)
        return 1; /* Out of bounds frames are considered used */
    
    return (phys_mem_mgr.bitmap[idx] & (1 << off)) ? 1 : 0;
}

/**
 * Find the first free frame starting from a given frame
 * @param start_frame Frame to start searching from
 * @return Frame number of first free frame, or -1 if none found
 */
static int phys_find_free_frame(uint32_t start_frame)
{
    uint32_t i, j;
    uint32_t start_idx = start_frame / 32;
    uint32_t start_off = start_frame % 32;
    
    /* Search from start_frame to end */
    for (i = start_idx; i < (phys_mem_mgr.total_frames + 31) / 32; i++) {
        if (phys_mem_mgr.bitmap[i] != 0xFFFFFFFF) {
            /* Found a word with at least one free bit */
            uint32_t start_bit = (i == start_idx) ? start_off : 0;
            
            for (j = start_bit; j < 32; j++) {
                if (!(phys_mem_mgr.bitmap[i] & (1 << j))) {
                    uint32_t frame = i * 32 + j;
                    if (frame < phys_mem_mgr.total_frames) {
                        return frame;
                    }
                }
            }
        }
    }
    
    /* If we started from a non-zero frame, search from beginning */
    if (start_frame > 0) {
        return phys_find_free_frame(0);
    }
    
    return -1; /* No free frames found */
}

/**
 * Allocate a physical frame
 * @return Physical address of allocated frame, or 0 if none available
 */
uint32_t phys_alloc_frame(void)
{
    int frame;
    
    if (phys_mem_mgr.free_frames == 0) {
        PANIC("Out of physical memory!");
        return 0;
    }
    
    /* Find first free frame */
    frame = phys_find_free_frame(phys_mem_mgr.first_free_frame);
    
    if (frame == -1) {
        PANIC("Physical memory corruption - free_frames > 0 but no free frame found!");
        return 0;
    }
    
    /* Mark frame as used */
    phys_set_frame(frame * PAGE_SIZE);
    
    /* Update first free frame hint */
    if ((uint32_t)frame == phys_mem_mgr.first_free_frame) {
        phys_mem_mgr.first_free_frame = frame + 1;
    }
    
    return frame * PAGE_SIZE;
}

/**
 * Free a physical frame
 * @param frame_addr Physical address of frame to free
 */
void phys_free_frame(uint32_t frame_addr)
{
    if (frame_addr == 0) {
        WARN("Attempted to free NULL frame");
        return;
    }
    
    if (frame_addr % PAGE_SIZE != 0) {
        WARN("Attempted to free unaligned frame address");
        return;
    }
    
    if (!phys_test_frame(frame_addr)) {
        WARN("Attempted to free already free frame");
        return;
    }
    
    phys_clear_frame(frame_addr);
}

/**
 * Get number of free frames
 * @return Number of free frames
 */
uint32_t phys_get_free_frames(void)
{
    return phys_mem_mgr.free_frames;
}

/**
 * Get total memory size in bytes
 * @return Total memory size
 */
uint32_t phys_get_total_memory(void)
{
    return phys_mem_mgr.total_frames * PAGE_SIZE;
}

/**
 * Get used memory size in bytes
 * @return Used memory size
 */
uint32_t phys_get_used_memory(void)
{
    return phys_mem_mgr.used_frames * PAGE_SIZE;
}

/**
 * Get free memory size in bytes
 * @return Free memory size
 */
uint32_t phys_get_free_memory(void)
{
    return phys_mem_mgr.free_frames * PAGE_SIZE;
}

/**
 * Print physical memory statistics
 */
void phys_print_stats(void)
{
    terminal_writestring("=== Physical Memory Statistics ===\n");
    terminal_writestring("Total frames: ");
    printnbr(phys_mem_mgr.total_frames, 10);
    terminal_writestring(" (");
    printnbr(phys_get_total_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB)\n");
    
    terminal_writestring("Used frames:  ");
    printnbr(phys_mem_mgr.used_frames, 10);
    terminal_writestring(" (");
    printnbr(phys_get_used_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB)\n");
    
    terminal_writestring("Free frames:  ");
    printnbr(phys_mem_mgr.free_frames, 10);
    terminal_writestring(" (");
    printnbr(phys_get_free_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB)\n");
    
    terminal_writestring("First free frame: ");
    printnbr(phys_mem_mgr.first_free_frame, 10);
    terminal_writestring("\n");
}
