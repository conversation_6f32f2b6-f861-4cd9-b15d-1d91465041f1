/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   vmalloc.c                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/* Virtual memory area structure */
typedef struct s_vm_area {
    uint32_t start_addr;        /* Start virtual address */
    uint32_t end_addr;          /* End virtual address */
    uint32_t size;              /* Size in bytes */
    uint32_t flags;             /* Page flags */
    struct s_vm_area *next;     /* Next area in list */
    struct s_vm_area *prev;     /* Previous area in list */
} t_vm_area;

/* Virtual memory manager */
typedef struct s_vm_manager {
    t_vm_area *areas;           /* List of allocated areas */
    uint32_t next_addr;         /* Next available virtual address */
    uint32_t total_allocated;   /* Total allocated virtual memory */
} t_vm_manager;

/* Global virtual memory manager */
static t_vm_manager vm_mgr = {0};

/* Virtual memory space start (above kernel heap) */
#define VM_START (KERNEL_HEAP_START + KERNEL_HEAP_SIZE)
#define VM_END   USER_SPACE_START

/**
 * Initialize virtual memory manager
 */
static void vm_init(void)
{
    static int initialized = 0;
    
    if (initialized) {
        return;
    }
    
    vm_mgr.areas = NULL;
    vm_mgr.next_addr = VM_START;
    vm_mgr.total_allocated = 0;
    
    initialized = 1;
    
    terminal_writestring("Virtual memory manager initialized\n");
    terminal_writestring("VM space: 0x");
    printnbr(VM_START, 16);
    terminal_writestring(" - 0x");
    printnbr(VM_END, 16);
    terminal_writestring("\n");
}

/**
 * Find a virtual memory area by address
 * @param addr Virtual address to search for
 * @return Pointer to VM area, or NULL if not found
 */
static t_vm_area* vm_find_area(uint32_t addr)
{
    t_vm_area *area = vm_mgr.areas;
    
    while (area) {
        if (addr >= area->start_addr && addr < area->end_addr) {
            return area;
        }
        area = area->next;
    }
    
    return NULL;
}

/**
 * Add a new virtual memory area to the list
 * @param area VM area to add
 */
static void vm_add_area(t_vm_area *area)
{
    area->next = vm_mgr.areas;
    area->prev = NULL;
    
    if (vm_mgr.areas) {
        vm_mgr.areas->prev = area;
    }
    
    vm_mgr.areas = area;
}

/**
 * Remove a virtual memory area from the list
 * @param area VM area to remove
 */
static void vm_remove_area(t_vm_area *area)
{
    if (area->prev) {
        area->prev->next = area->next;
    } else {
        vm_mgr.areas = area->next;
    }
    
    if (area->next) {
        area->next->prev = area->prev;
    }
}

/**
 * Allocate virtual memory
 * @param size Size to allocate in bytes
 * @return Virtual address of allocated memory, or NULL on failure
 */
void* vmalloc(size_t size)
{
    t_vm_area *area;
    uint32_t start_addr;
    uint32_t num_pages;
    uint32_t i;
    
    vm_init();
    
    if (size == 0) {
        return NULL;
    }
    
    /* Round up to page boundary */
    size = (size + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
    num_pages = size / PAGE_SIZE;
    
    /* Check if we have enough virtual address space */
    if (vm_mgr.next_addr + size >= VM_END) {
        WARN("vmalloc: Out of virtual address space");
        return NULL;
    }
    
    /* Allocate VM area structure */
    area = (t_vm_area*)kmalloc(sizeof(t_vm_area));
    if (!area) {
        WARN("vmalloc: Failed to allocate VM area structure");
        return NULL;
    }
    
    start_addr = vm_mgr.next_addr;
    
    /* Allocate physical pages and map them */
    for (i = 0; i < num_pages; i++) {
        uint32_t phys_addr = phys_alloc_frame();
        uint32_t virt_addr = start_addr + (i * PAGE_SIZE);
        
        if (phys_addr == 0) {
            /* Failed to allocate physical memory, cleanup */
            for (uint32_t j = 0; j < i; j++) {
                uint32_t cleanup_virt = start_addr + (j * PAGE_SIZE);
                uint32_t cleanup_phys = paging_get_physical_addr(cleanup_virt);
                paging_unmap_page(cleanup_virt);
                phys_free_frame(cleanup_phys);
            }
            kfree(area);
            WARN("vmalloc: Failed to allocate physical memory");
            return NULL;
        }
        
        /* Map virtual page to physical page */
        if (paging_map_page(virt_addr, phys_addr, PAGE_PRESENT | PAGE_WRITABLE) != 0) {
            /* Failed to map page, cleanup */
            phys_free_frame(phys_addr);
            for (uint32_t j = 0; j < i; j++) {
                uint32_t cleanup_virt = start_addr + (j * PAGE_SIZE);
                uint32_t cleanup_phys = paging_get_physical_addr(cleanup_virt);
                paging_unmap_page(cleanup_virt);
                phys_free_frame(cleanup_phys);
            }
            kfree(area);
            WARN("vmalloc: Failed to map virtual page");
            return NULL;
        }
    }
    
    /* Initialize VM area */
    area->start_addr = start_addr;
    area->end_addr = start_addr + size;
    area->size = size;
    area->flags = PAGE_PRESENT | PAGE_WRITABLE;
    
    /* Add to area list */
    vm_add_area(area);
    
    /* Update manager state */
    vm_mgr.next_addr += size;
    vm_mgr.total_allocated += size;
    
    /* Zero the allocated memory */
    memset((void*)start_addr, 0, size);
    
    return (void*)start_addr;
}

/**
 * Free virtual memory
 * @param ptr Virtual address to free
 */
void vfree(void *ptr)
{
    t_vm_area *area;
    uint32_t addr = (uint32_t)ptr;
    uint32_t num_pages;
    uint32_t i;
    
    if (!ptr) {
        return;
    }
    
    /* Find the VM area */
    area = vm_find_area(addr);
    if (!area) {
        WARN("vfree: Invalid virtual address");
        return;
    }
    
    if (addr != area->start_addr) {
        WARN("vfree: Address is not start of allocated area");
        return;
    }
    
    num_pages = area->size / PAGE_SIZE;
    
    /* Unmap and free all pages */
    for (i = 0; i < num_pages; i++) {
        uint32_t virt_addr = area->start_addr + (i * PAGE_SIZE);
        uint32_t phys_addr = paging_get_physical_addr(virt_addr);
        
        if (phys_addr != 0) {
            paging_unmap_page(virt_addr);
            phys_free_frame(phys_addr);
        }
    }
    
    /* Update manager state */
    vm_mgr.total_allocated -= area->size;
    
    /* Remove from area list */
    vm_remove_area(area);
    
    /* Free the area structure */
    kfree(area);
}

/**
 * Get size of virtual memory allocation
 * @param ptr Virtual address
 * @return Size of allocation, or 0 if invalid
 */
size_t vsize(void *ptr)
{
    t_vm_area *area;
    uint32_t addr = (uint32_t)ptr;
    
    if (!ptr) {
        return 0;
    }
    
    area = vm_find_area(addr);
    if (!area) {
        return 0;
    }
    
    if (addr != area->start_addr) {
        return 0; /* Not start of allocation */
    }
    
    return area->size;
}

/**
 * Virtual memory break (simplified implementation)
 * @param increment Amount to increase/decrease virtual memory
 * @return Previous break value, or NULL on failure
 */
void* vbrk(intptr_t increment)
{
    uint32_t old_addr;
    uint32_t new_addr;
    
    vm_init();
    
    old_addr = vm_mgr.next_addr;
    new_addr = vm_mgr.next_addr + increment;
    
    if (new_addr < VM_START || new_addr >= VM_END) {
        return NULL; /* Out of bounds */
    }
    
    vm_mgr.next_addr = new_addr;
    return (void*)old_addr;
}

/**
 * Get virtual memory statistics
 * @param total_allocated Pointer to store total allocated bytes
 * @param num_areas Pointer to store number of allocated areas
 */
void vm_get_stats(uint32_t *total_allocated, uint32_t *num_areas)
{
    t_vm_area *area;
    uint32_t count = 0;
    
    vm_init();
    
    if (total_allocated) {
        *total_allocated = vm_mgr.total_allocated;
    }
    
    if (num_areas) {
        area = vm_mgr.areas;
        while (area) {
            count++;
            area = area->next;
        }
        *num_areas = count;
    }
}

/**
 * Print virtual memory statistics
 */
void vm_print_stats(void)
{
    uint32_t total_allocated, num_areas;
    
    vm_get_stats(&total_allocated, &num_areas);
    
    terminal_writestring("=== Virtual Memory Statistics ===\n");
    terminal_writestring("Total allocated: ");
    printnbr(total_allocated / 1024, 10);
    terminal_writestring(" KB (");
    printnbr(total_allocated, 10);
    terminal_writestring(" bytes)\n");
    
    terminal_writestring("Number of areas: ");
    printnbr(num_areas, 10);
    terminal_writestring("\n");
    
    terminal_writestring("Next address: 0x");
    printnbr(vm_mgr.next_addr, 16);
    terminal_writestring("\n");
}

/**
 * Dump all virtual memory areas (for debugging)
 */
void vm_dump_areas(void)
{
    t_vm_area *area = vm_mgr.areas;
    int count = 0;
    
    terminal_writestring("=== Virtual Memory Areas ===\n");
    
    while (area) {
        terminal_writestring("Area ");
        printnbr(count, 10);
        terminal_writestring(": 0x");
        printnbr(area->start_addr, 16);
        terminal_writestring(" - 0x");
        printnbr(area->end_addr, 16);
        terminal_writestring(" (");
        printnbr(area->size / 1024, 10);
        terminal_writestring(" KB)\n");
        
        area = area->next;
        count++;
    }
    
    if (count == 0) {
        terminal_writestring("No virtual memory areas allocated\n");
    }
}
