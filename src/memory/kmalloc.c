/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kmalloc.c                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/* Global kernel heap */
t_kernel_heap kernel_heap = {0};

/* Magic number for block validation */
#define HEAP_MAGIC 0xDEADBEEF

/* Minimum block size (including header) */
#define MIN_BLOCK_SIZE (sizeof(t_mem_block) + 16)

/* Current heap break (end of allocated heap) */
static uint32_t heap_break = KERNEL_HEAP_START;

/**
 * Initialize the kernel heap
 */
void heap_init(void)
{
    t_mem_block *initial_block;
    
    kernel_heap.start_addr = KERNEL_HEAP_START;
    kernel_heap.end_addr = KERNEL_HEAP_START + KERNEL_HEAP_SIZE;
    kernel_heap.size = KERNEL_HEAP_SIZE;
    kernel_heap.used = 0;
    kernel_heap.free = KERNEL_HEAP_SIZE;
    
    /* Create initial free block */
    initial_block = (t_mem_block*)KERNEL_HEAP_START;
    initial_block->size = KERNEL_HEAP_SIZE - sizeof(t_mem_block);
    initial_block->magic = HEAP_MAGIC;
    initial_block->next = NULL;
    initial_block->prev = NULL;
    initial_block->free = 1;
    
    kernel_heap.first_block = initial_block;
    
    terminal_writestring("Kernel heap initialized at 0x");
    printnbr(KERNEL_HEAP_START, 16);
    terminal_writestring(" - 0x");
    printnbr(kernel_heap.end_addr, 16);
    terminal_writestring("\n");
}

/**
 * Find a free block of at least the specified size
 * @param size Minimum size needed
 * @return Pointer to free block, or NULL if none found
 */
static t_mem_block* heap_find_free_block(size_t size)
{
    t_mem_block *current = kernel_heap.first_block;
    
    while (current) {
        if (current->free && current->size >= size) {
            return current;
        }
        current = current->next;
    }
    
    return NULL;
}

/**
 * Split a block if it's larger than needed
 * @param block Block to split
 * @param size Size needed for first part
 */
static void heap_split_block(t_mem_block *block, size_t size)
{
    t_mem_block *new_block;
    size_t remaining_size;
    
    if (block->size < size + MIN_BLOCK_SIZE) {
        /* Not enough space to split */
        return;
    }
    
    remaining_size = block->size - size - sizeof(t_mem_block);
    
    /* Create new block for remaining space */
    new_block = (t_mem_block*)((uint8_t*)block + sizeof(t_mem_block) + size);
    new_block->size = remaining_size;
    new_block->magic = HEAP_MAGIC;
    new_block->next = block->next;
    new_block->prev = block;
    new_block->free = 1;
    
    /* Update original block */
    block->size = size;
    block->next = new_block;
    
    /* Update next block's prev pointer */
    if (new_block->next) {
        new_block->next->prev = new_block;
    }
}

/**
 * Merge adjacent free blocks
 * @param block Block to merge with adjacent blocks
 */
static void heap_merge_blocks(t_mem_block *block)
{
    /* Merge with next block if it's free */
    while (block->next && block->next->free) {
        t_mem_block *next = block->next;
        
        block->size += sizeof(t_mem_block) + next->size;
        block->next = next->next;
        
        if (next->next) {
            next->next->prev = block;
        }
    }
    
    /* Merge with previous block if it's free */
    if (block->prev && block->prev->free) {
        t_mem_block *prev = block->prev;
        
        prev->size += sizeof(t_mem_block) + block->size;
        prev->next = block->next;
        
        if (block->next) {
            block->next->prev = prev;
        }
    }
}

/**
 * Validate a memory block
 * @param block Block to validate
 * @return 1 if valid, 0 if invalid
 */
static int heap_validate_block(t_mem_block *block)
{
    if (!block) {
        return 0;
    }
    
    if (block->magic != HEAP_MAGIC) {
        return 0;
    }
    
    if ((uint32_t)block < kernel_heap.start_addr || 
        (uint32_t)block >= kernel_heap.end_addr) {
        return 0;
    }
    
    return 1;
}

/**
 * Allocate memory from kernel heap
 * @param size Size to allocate
 * @return Pointer to allocated memory, or NULL on failure
 */
void* kmalloc(size_t size)
{
    t_mem_block *block;
    
    if (size == 0) {
        return NULL;
    }
    
    /* Align size to 4-byte boundary */
    size = (size + 3) & ~3;
    
    /* Find a free block */
    block = heap_find_free_block(size);
    if (!block) {
        WARN("kmalloc: Out of heap memory");
        return NULL;
    }
    
    /* Split block if necessary */
    heap_split_block(block, size);
    
    /* Mark block as used */
    block->free = 0;
    
    /* Update heap statistics */
    kernel_heap.used += block->size;
    kernel_heap.free -= block->size;
    
    /* Return pointer to data (after header) */
    return (void*)((uint8_t*)block + sizeof(t_mem_block));
}

/**
 * Allocate aligned memory from kernel heap
 * @param size Size to allocate
 * @return Pointer to page-aligned allocated memory, or NULL on failure
 */
void* kmalloc_a(size_t size)
{
    void *ptr = kmalloc(size + PAGE_SIZE);
    if (!ptr) {
        return NULL;
    }
    
    /* Align to page boundary */
    uint32_t aligned = ((uint32_t)ptr + PAGE_SIZE - 1) & ~(PAGE_SIZE - 1);
    return (void*)aligned;
}

/**
 * Allocate memory and return physical address
 * @param size Size to allocate
 * @param phys_addr Pointer to store physical address
 * @return Pointer to allocated memory, or NULL on failure
 */
void* kmalloc_p(size_t size, uint32_t *phys_addr)
{
    void *ptr = kmalloc(size);
    if (ptr && phys_addr) {
        *phys_addr = paging_get_physical_addr((uint32_t)ptr);
    }
    return ptr;
}

/**
 * Allocate aligned memory and return physical address
 * @param size Size to allocate
 * @param phys_addr Pointer to store physical address
 * @return Pointer to page-aligned allocated memory, or NULL on failure
 */
void* kmalloc_ap(size_t size, uint32_t *phys_addr)
{
    void *ptr = kmalloc_a(size);
    if (ptr && phys_addr) {
        *phys_addr = paging_get_physical_addr((uint32_t)ptr);
    }
    return ptr;
}

/**
 * Free memory allocated by kmalloc
 * @param ptr Pointer to memory to free
 */
void kfree(void *ptr)
{
    t_mem_block *block;
    
    if (!ptr) {
        return;
    }
    
    /* Get block header */
    block = (t_mem_block*)((uint8_t*)ptr - sizeof(t_mem_block));
    
    /* Validate block */
    if (!heap_validate_block(block)) {
        WARN("kfree: Invalid block or double free detected");
        return;
    }
    
    if (block->free) {
        WARN("kfree: Attempted to free already free block");
        return;
    }
    
    /* Mark block as free */
    block->free = 1;
    
    /* Update heap statistics */
    kernel_heap.used -= block->size;
    kernel_heap.free += block->size;
    
    /* Merge with adjacent free blocks */
    heap_merge_blocks(block);
}

/**
 * Get size of allocated memory block
 * @param ptr Pointer to memory block
 * @return Size of block, or 0 if invalid
 */
size_t ksize(void *ptr)
{
    t_mem_block *block;
    
    if (!ptr) {
        return 0;
    }
    
    block = (t_mem_block*)((uint8_t*)ptr - sizeof(t_mem_block));
    
    if (!heap_validate_block(block)) {
        return 0;
    }
    
    return block->size;
}

/**
 * Allocate and zero memory
 * @param num Number of elements
 * @param size Size of each element
 * @return Pointer to zeroed memory, or NULL on failure
 */
void* kcalloc(size_t num, size_t size)
{
    size_t total_size = num * size;
    void *ptr = kmalloc(total_size);
    
    if (ptr) {
        memset(ptr, 0, total_size);
    }
    
    return ptr;
}

/**
 * Reallocate memory
 * @param ptr Existing memory pointer
 * @param size New size
 * @return Pointer to reallocated memory, or NULL on failure
 */
void* krealloc(void *ptr, size_t size)
{
    void *new_ptr;
    size_t old_size;
    
    if (!ptr) {
        return kmalloc(size);
    }
    
    if (size == 0) {
        kfree(ptr);
        return NULL;
    }
    
    old_size = ksize(ptr);
    if (old_size == 0) {
        return NULL; /* Invalid pointer */
    }
    
    new_ptr = kmalloc(size);
    if (!new_ptr) {
        return NULL;
    }
    
    /* Copy old data */
    memcpy(new_ptr, ptr, (old_size < size) ? old_size : size);
    
    kfree(ptr);
    return new_ptr;
}

/**
 * Adjust heap break (simple implementation)
 * @param increment Amount to increase/decrease heap
 * @return Previous break value, or NULL on failure
 */
void* kbrk(intptr_t increment)
{
    uint32_t old_break = heap_break;
    uint32_t new_break = heap_break + increment;
    
    if (new_break < KERNEL_HEAP_START || 
        new_break >= kernel_heap.end_addr) {
        return NULL; /* Out of bounds */
    }
    
    heap_break = new_break;
    return (void*)old_break;
}
