/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   panic.c                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/* Panic statistics */
static struct {
    uint32_t total_panics;
    uint32_t info_count;
    uint32_t warning_count;
    uint32_t error_count;
    uint32_t critical_count;
    uint32_t fatal_count;
} panic_stats = {0};

/* Color codes for different panic levels */
static const uint8_t panic_colors[] = {
    VGA_COLOR_LIGHT_BLUE,    /* INFO */
    VGA_COLOR_LIGHT_BROWN,   /* WARNING */
    VGA_COLOR_LIGHT_RED,     /* ERROR */
    VGA_COLOR_RED,           /* CRITICAL */
    VGA_COLOR_WHITE          /* FATAL */
};

/* Panic level names */
static const char* panic_level_names[] = {
    "INFO",
    "WARNING", 
    "ERROR",
    "CRITICAL",
    "FATAL"
};

/**
 * Get current stack pointer
 * @return Current ESP value
 */
static uint32_t get_stack_pointer(void)
{
    uint32_t esp;
    __asm__ volatile("mov %%esp, %0" : "=r"(esp));
    return esp;
}

/**
 * Get current base pointer
 * @return Current EBP value
 */
static uint32_t get_base_pointer(void)
{
    uint32_t ebp;
    __asm__ volatile("mov %%ebp, %0" : "=r"(ebp));
    return ebp;
}

/**
 * Print a stack trace (simplified version)
 */
void print_stack_trace(void)
{
    uint32_t *ebp = (uint32_t*)get_base_pointer();
    uint32_t esp = get_stack_pointer();
    int frame = 0;
    
    terminal_writestring("\n=== Stack Trace ===\n");
    terminal_writestring("ESP: 0x");
    printnbr(esp, 16);
    terminal_writestring("\n");
    
    /* Walk the stack frames */
    while (ebp && frame < 10) {
        uint32_t eip = ebp[1]; /* Return address */
        
        terminal_writestring("Frame ");
        printnbr(frame, 10);
        terminal_writestring(": EBP=0x");
        printnbr((uint32_t)ebp, 16);
        terminal_writestring(" EIP=0x");
        printnbr(eip, 16);
        terminal_writestring("\n");
        
        /* Move to next frame */
        ebp = (uint32_t*)ebp[0];
        frame++;
        
        /* Sanity check to prevent infinite loops */
        if ((uint32_t)ebp < 0x100000 || (uint32_t)ebp > 0x8000000) {
            break;
        }
    }
    
    if (frame == 0) {
        terminal_writestring("Unable to trace stack\n");
    }
}

/**
 * Print memory information
 */
void print_memory_info(void)
{
    
    terminal_writestring("\n=== Memory Information ===\n");
    
    /* Physical memory stats */
    terminal_writestring("Physical Memory:\n");
    terminal_writestring("  Total: ");
    printnbr(phys_get_total_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    terminal_writestring("  Used:  ");
    printnbr(phys_get_used_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    terminal_writestring("  Free:  ");
    printnbr(phys_get_free_memory() / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    
    /* Kernel heap stats */
    terminal_writestring("Kernel Heap:\n");
    terminal_writestring("  Total: ");
    printnbr(kernel_heap.size / 1024, 10);
    terminal_writestring(" KB\n");
    terminal_writestring("  Used:  ");
    printnbr(kernel_heap.used / 1024, 10);
    terminal_writestring(" KB\n");
    terminal_writestring("  Free:  ");
    printnbr(kernel_heap.free / 1024, 10);
    terminal_writestring(" KB\n");
    
    /* Virtual memory stats */
    uint32_t vm_allocated, vm_areas;
    vm_get_stats(&vm_allocated, &vm_areas);
    terminal_writestring("Virtual Memory:\n");
    terminal_writestring("  Allocated: ");
    printnbr(vm_allocated / 1024, 10);
    terminal_writestring(" KB\n");
    terminal_writestring("  Areas: ");
    printnbr(vm_areas, 10);
    terminal_writestring("\n");
}

/**
 * Print CPU registers (simplified - would need interrupt context)
 */
static void print_cpu_registers(void)
{
    uint32_t cr0, cr2, cr3;
    
    terminal_writestring("\n=== CPU Registers ===\n");
    
    /* Control registers */
    __asm__ volatile("mov %%cr0, %0" : "=r"(cr0));
    __asm__ volatile("mov %%cr2, %0" : "=r"(cr2));
    __asm__ volatile("mov %%cr3, %0" : "=r"(cr3));
    
    terminal_writestring("CR0: 0x");
    printnbr(cr0, 16);
    terminal_writestring(" (Paging: ");
    terminal_writestring((cr0 & 0x80000000) ? "ON" : "OFF");
    terminal_writestring(")\n");
    
    terminal_writestring("CR2: 0x");
    printnbr(cr2, 16);
    terminal_writestring(" (Page fault address)\n");
    
    terminal_writestring("CR3: 0x");
    printnbr(cr3, 16);
    terminal_writestring(" (Page directory)\n");
    
    terminal_writestring("ESP: 0x");
    printnbr(get_stack_pointer(), 16);
    terminal_writestring("\n");
    
    terminal_writestring("EBP: 0x");
    printnbr(get_base_pointer(), 16);
    terminal_writestring("\n");
}

/**
 * Print panic statistics
 */
static void print_panic_stats(void)
{
    terminal_writestring("\n=== Panic Statistics ===\n");
    terminal_writestring("Total panics: ");
    printnbr(panic_stats.total_panics, 10);
    terminal_writestring("\n");
    terminal_writestring("INFO:     ");
    printnbr(panic_stats.info_count, 10);
    terminal_writestring("\n");
    terminal_writestring("WARNING:  ");
    printnbr(panic_stats.warning_count, 10);
    terminal_writestring("\n");
    terminal_writestring("ERROR:    ");
    printnbr(panic_stats.error_count, 10);
    terminal_writestring("\n");
    terminal_writestring("CRITICAL: ");
    printnbr(panic_stats.critical_count, 10);
    terminal_writestring("\n");
    terminal_writestring("FATAL:    ");
    printnbr(panic_stats.fatal_count, 10);
    terminal_writestring("\n");
}

/**
 * Main kernel panic function
 * @param level Panic level
 * @param message Panic message
 */
void kernel_panic(t_panic_level level, const char *message)
{
    uint8_t old_color;
    
    /* Disable interrupts to prevent further issues */
    DisableInterrupts();
    
    /* Update statistics */
    panic_stats.total_panics++;
    switch (level) {
        case PANIC_INFO:     panic_stats.info_count++; break;
        case PANIC_WARNING:  panic_stats.warning_count++; break;
        case PANIC_ERROR:    panic_stats.error_count++; break;
        case PANIC_CRITICAL: panic_stats.critical_count++; break;
        case PANIC_FATAL:    panic_stats.fatal_count++; break;
    }
    
    /* Save current color and set panic color */
    old_color = kernel.screens[kernel.screen_index].color;
    kernel.screens[kernel.screen_index].color = 
        vga_entry_color(panic_colors[level], VGA_COLOR_BLACK);
    
    /* Print panic header */
    terminal_writestring("\n*** KERNEL PANIC [");
    terminal_writestring(panic_level_names[level]);
    terminal_writestring("] ***\n");
    terminal_writestring("Message: ");
    terminal_writestring(message);
    terminal_writestring("\n");
    
    /* For serious panics, print detailed information */
    if (level >= PANIC_ERROR) {
        print_cpu_registers();
        print_stack_trace();
        print_memory_info();
    }
    
    /* For fatal panics, print everything and halt */
    if (level == PANIC_FATAL) {
        print_panic_stats();
        terminal_writestring("\n*** SYSTEM HALTED ***\n");
        
        /* Restore color */
        kernel.screens[kernel.screen_index].color = old_color;
        
        /* Halt the system */
        while (1) {
            __asm__ volatile("hlt");
        }
    }
    
    /* For non-fatal panics, restore color and continue */
    kernel.screens[kernel.screen_index].color = old_color;
    
    /* Re-enable interrupts for non-fatal panics */
    if (level < PANIC_CRITICAL) {
        EnableInterrupts();
    }
}

/**
 * Kernel panic with file and line information
 * @param level Panic level
 * @param file Source file name
 * @param line Line number
 * @param message Panic message
 */
void kernel_panic_at(t_panic_level level, const char *file, int line, const char *message)
{
    char location_msg[256];
    int i = 0, j = 0;
    
    /* Build location message */
    while (file[i] && j < 200) {
        location_msg[j++] = file[i++];
    }
    location_msg[j++] = ':';
    
    /* Convert line number to string */
    if (line > 0) {
        char line_str[16];
        int k = 0, temp = line;
        
        /* Count digits */
        do {
            k++;
            temp /= 10;
        } while (temp > 0);
        
        /* Convert to string */
        line_str[k] = '\0';
        temp = line;
        while (k > 0) {
            line_str[--k] = '0' + (temp % 10);
            temp /= 10;
        }
        
        /* Append to location message */
        k = 0;
        while (line_str[k] && j < 220) {
            location_msg[j++] = line_str[k++];
        }
    }
    
    location_msg[j++] = ' ';
    location_msg[j++] = '-';
    location_msg[j++] = ' ';
    
    /* Append original message */
    i = 0;
    while (message[i] && j < 255) {
        location_msg[j++] = message[i++];
    }
    location_msg[j] = '\0';
    
    kernel_panic(level, location_msg);
}

/**
 * Get panic statistics
 * @param stats Pointer to structure to fill with statistics
 */
void get_panic_stats(t_panic_statistics *stats)
{
    if (!stats) return;
    
    stats->total_panics = panic_stats.total_panics;
    stats->info_count = panic_stats.info_count;
    stats->warning_count = panic_stats.warning_count;
    stats->error_count = panic_stats.error_count;
    stats->critical_count = panic_stats.critical_count;
    stats->fatal_count = panic_stats.fatal_count;
}
