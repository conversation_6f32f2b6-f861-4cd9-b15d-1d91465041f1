/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   memory_utils.c                                     :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../include/kernel.h"

/**
 * Set memory to a specific value
 * @param ptr Pointer to memory to set
 * @param value Value to set (only low byte is used)
 * @param size Number of bytes to set
 * @return Pointer to the memory area
 */
void *memset(void *ptr, int value, size_t size)
{
    unsigned char *p = (unsigned char*)ptr;
    unsigned char val = (unsigned char)value;
    
    if (!ptr || size == 0) {
        return ptr;
    }
    
    /* Set bytes one by one */
    while (size--) {
        *p++ = val;
    }
    
    return ptr;
}

/**
 * Copy memory from source to destination
 * @param dest Destination pointer
 * @param src Source pointer
 * @param size Number of bytes to copy
 * @return Pointer to destination
 */
void *memcpy(void *dest, const void *src, size_t size)
{
    unsigned char *d = (unsigned char*)dest;
    const unsigned char *s = (const unsigned char*)src;
    
    if (!dest || !src || size == 0) {
        return dest;
    }
    
    /* Check for overlap - if overlapping, use memmove logic */
    if (d < s || d >= s + size) {
        /* No overlap, copy forward */
        while (size--) {
            *d++ = *s++;
        }
    } else {
        /* Overlap detected, copy backward */
        d += size - 1;
        s += size - 1;
        while (size--) {
            *d-- = *s--;
        }
    }
    
    return dest;
}

/**
 * Move memory from source to destination (handles overlapping regions)
 * @param dest Destination pointer
 * @param src Source pointer
 * @param size Number of bytes to move
 * @return Pointer to destination
 */
void *memmove(void *dest, const void *src, size_t size)
{
    unsigned char *d = (unsigned char*)dest;
    const unsigned char *s = (const unsigned char*)src;
    
    if (!dest || !src || size == 0) {
        return dest;
    }
    
    if (d < s) {
        /* Copy forward */
        while (size--) {
            *d++ = *s++;
        }
    } else if (d > s) {
        /* Copy backward */
        d += size - 1;
        s += size - 1;
        while (size--) {
            *d-- = *s--;
        }
    }
    /* If d == s, no need to copy */
    
    return dest;
}

/**
 * Compare two memory areas
 * @param ptr1 First memory area
 * @param ptr2 Second memory area
 * @param size Number of bytes to compare
 * @return 0 if equal, <0 if ptr1 < ptr2, >0 if ptr1 > ptr2
 */
int memcmp(const void *ptr1, const void *ptr2, size_t size)
{
    const unsigned char *p1 = (const unsigned char*)ptr1;
    const unsigned char *p2 = (const unsigned char*)ptr2;
    
    if (!ptr1 || !ptr2) {
        if (ptr1 == ptr2) return 0;
        return ptr1 ? 1 : -1;
    }
    
    while (size--) {
        if (*p1 != *p2) {
            return (*p1 < *p2) ? -1 : 1;
        }
        p1++;
        p2++;
    }
    
    return 0;
}

/**
 * Find first occurrence of a byte in memory
 * @param ptr Memory area to search
 * @param value Byte value to find
 * @param size Number of bytes to search
 * @return Pointer to first occurrence, or NULL if not found
 */
void *memchr(const void *ptr, int value, size_t size)
{
    const unsigned char *p = (const unsigned char*)ptr;
    unsigned char val = (unsigned char)value;
    
    if (!ptr) {
        return NULL;
    }
    
    while (size--) {
        if (*p == val) {
            return (void*)p;
        }
        p++;
    }
    
    return NULL;
}

/**
 * Get memory statistics
 * @param stats Pointer to structure to fill with statistics
 */
void mem_get_stats(t_mem_stats *stats)
{
    if (!stats) {
        return;
    }
    
    /* Physical memory stats */
    stats->total_memory = phys_get_total_memory();
    stats->used_memory = phys_get_used_memory();
    stats->free_memory = phys_get_free_memory();
    
    /* Kernel memory (heap) stats */
    stats->kernel_memory = kernel_heap.used;
    
    /* User memory (simplified - would need process tracking) */
    stats->user_memory = 0;
    
    /* Cached memory (simplified - no cache implementation yet) */
    stats->cached_memory = 0;
}

/**
 * Print memory statistics
 */
void mem_print_stats(void)
{
    t_mem_stats stats;
    
    mem_get_stats(&stats);
    
    terminal_writestring("=== Memory Statistics ===\n");
    terminal_writestring("Total Memory:  ");
    printnbr(stats.total_memory / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    
    terminal_writestring("Used Memory:   ");
    printnbr(stats.used_memory / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    
    terminal_writestring("Free Memory:   ");
    printnbr(stats.free_memory / 1024 / 1024, 10);
    terminal_writestring(" MB\n");
    
    terminal_writestring("Kernel Memory: ");
    printnbr(stats.kernel_memory / 1024, 10);
    terminal_writestring(" KB\n");
    
    terminal_writestring("User Memory:   ");
    printnbr(stats.user_memory / 1024, 10);
    terminal_writestring(" KB\n");
}

/**
 * Dump page directory contents (for debugging)
 * @param dir Page directory to dump
 */
void mem_dump_page_directory(t_page_directory *dir)
{
    int i, count = 0;
    
    if (!dir) {
        terminal_writestring("Page directory is NULL\n");
        return;
    }
    
    terminal_writestring("=== Page Directory Dump ===\n");
    terminal_writestring("Physical address: 0x");
    printnbr(dir->physical_addr, 16);
    terminal_writestring("\n");
    
    for (i = 0; i < PAGE_ENTRIES && count < 20; i++) {
        if (dir->entries[i].present) {
            terminal_writestring("Entry ");
            printnbr(i, 10);
            terminal_writestring(": Frame=0x");
            printnbr(dir->entries[i].frame, 16);
            terminal_writestring(" Flags=");
            if (dir->entries[i].writable) terminal_writestring("W");
            if (dir->entries[i].user) terminal_writestring("U");
            if (dir->entries[i].page_size) terminal_writestring("4M");
            terminal_writestring("\n");
            count++;
        }
    }
    
    if (count == 0) {
        terminal_writestring("No present pages found\n");
    } else if (count == 20) {
        terminal_writestring("... (showing first 20 entries)\n");
    }
}

/**
 * Dump kernel heap contents (for debugging)
 */
void mem_dump_heap(void)
{
    t_mem_block *block = kernel_heap.first_block;
    int count = 0;
    
    terminal_writestring("=== Kernel Heap Dump ===\n");
    terminal_writestring("Heap start: 0x");
    printnbr(kernel_heap.start_addr, 16);
    terminal_writestring("\n");
    terminal_writestring("Heap end:   0x");
    printnbr(kernel_heap.end_addr, 16);
    terminal_writestring("\n");
    terminal_writestring("Total size: ");
    printnbr(kernel_heap.size / 1024, 10);
    terminal_writestring(" KB\n");
    terminal_writestring("Used:       ");
    printnbr(kernel_heap.used / 1024, 10);
    terminal_writestring(" KB\n");
    terminal_writestring("Free:       ");
    printnbr(kernel_heap.free / 1024, 10);
    terminal_writestring(" KB\n\n");
    
    terminal_writestring("Blocks:\n");
    while (block && count < 10) {
        terminal_writestring("Block ");
        printnbr(count, 10);
        terminal_writestring(": Addr=0x");
        printnbr((uint32_t)block, 16);
        terminal_writestring(" Size=");
        printnbr(block->size, 10);
        terminal_writestring(" ");
        terminal_writestring(block->free ? "FREE" : "USED");
        terminal_writestring("\n");
        
        block = block->next;
        count++;
    }
    
    if (count == 10 && block) {
        terminal_writestring("... (showing first 10 blocks)\n");
    }
}

/**
 * Initialize memory management system
 * @param mem_size Total memory size in bytes
 */
void memory_init(uint32_t mem_size)
{
    terminal_writestring("Initializing memory management system...\n");
    
    /* Initialize physical memory manager */
    phys_mem_init(mem_size);
    
    /* Initialize paging */
    paging_init();
    
    /* Initialize kernel heap */
    heap_init();
    
    terminal_writestring("Memory management system initialized successfully!\n");
    
    /* Print initial statistics */
    mem_print_stats();
}
