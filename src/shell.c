/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.c                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/17 21:58:20 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/01 20:24:52 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../include/kernel.h"

#define COMMAND_BUFFER_SIZE 256
static char command_buffer[COMMAND_BUFFER_SIZE];
static int buffer_pos = 0;

typedef enum {
    CMD_HELP,
    CMD_STACK,
    CMD_PUSH,
    CMD_POP,
    CMD_CLEAR,
    CMD_REBOOT,
    CMD_HALT,
    CMD_SHUTDOWN,
    CMD_MEMSTAT,
    CMD_PHYSTAT,
    CMD_VMSTAT,
    CMD_HEAPDUMP,
    CMD_PAGEDUMP,
    CMD_KMALLOC,
    CMD_KFREE,
    CMD_VMALLOC,
    CMD_VFREE,
    CMD_PANIC,
    CMD_UNKNOWN
} command_type_t;

void shell_initialize() {
    terminal_writestring("KFS Shell v1.0\n");
    terminal_writestring("Type 'help' for available commands\n");
    terminal_writestring("> ");
}

void handle_help() {
    terminal_writestring("Available commands:\n");
    terminal_writestring("  help         - Display this help message\n");
    terminal_writestring("  stack        - Print the kernel stack\n");
    terminal_writestring("  push <hex>   - Push a value onto the stack\n");
    terminal_writestring("  pop          - Pop a value from the stack\n");
    terminal_writestring("  clear        - Clear the screen\n");
    terminal_writestring("  reboot       - Reboot the system\n");
    terminal_writestring("  halt         - Halt the system\n");
    terminal_writestring("  shutdown     - Shutdown the system\n");
    terminal_writestring("\nMemory commands:\n");
    terminal_writestring("  memstat      - Show memory statistics\n");
    terminal_writestring("  phystat      - Show physical memory stats\n");
    terminal_writestring("  vmstat       - Show virtual memory stats\n");
    terminal_writestring("  heapdump     - Dump kernel heap contents\n");
    terminal_writestring("  pagedump     - Dump page directory\n");
    terminal_writestring("  kmalloc <sz> - Allocate kernel memory\n");
    terminal_writestring("  kfree <addr> - Free kernel memory\n");
    terminal_writestring("  vmalloc <sz> - Allocate virtual memory\n");
    terminal_writestring("  vfree <addr> - Free virtual memory\n");
    terminal_writestring("  panic <lvl>  - Test panic system (0-4)\n");
}

void handle_stack() {
    print_kernel_stack();
}

void handle_push(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: push requires a hexadecimal value\n");
        return;
    }

    uint32_t value = 0;
    int i = 0;

    if (arg[0] == '0' && (arg[1] == 'x' || arg[1] == 'X'))
        i = 2;

    while (arg[i] != '\0') {
        value = value * 16;

        if (arg[i] >= '0' && arg[i] <= '9')
            value += arg[i] - '0';
        else if (arg[i] >= 'a' && arg[i] <= 'f')
            value += arg[i] - 'a' + 10;
        else if (arg[i] >= 'A' && arg[i] <= 'F')
            value += arg[i] - 'A' + 10;
        else {
            terminal_writestring("Error: Invalid hex value\n");
            return;
        }
        i++;
    }

    if (value != 0 || (arg[0] == '0' && arg[1] == '\0')) {
        stack_push(value);
        terminal_writestring("Pushed 0x");

        for (int j = 7; j >= 0; j--) {
            uint8_t nibble = (value >> (j * 4)) & 0xF;
            char hex_char;
            if (nibble < 10)
                hex_char = '0' + nibble;
            else
                hex_char = 'A' + (nibble - 10);
            terminal_putchar(hex_char);
        }

        terminal_writestring(" onto the stack\n");
    }
}

void handle_pop() {
    if (stack_is_empty()) {
        terminal_writestring("Error: Stack is empty\n");
        return;
    }

    uint32_t value = stack_pop();
    terminal_writestring("Popped 0x");

    for (int j = 7; j >= 0; j--) {
        uint8_t nibble = (value >> (j * 4)) & 0xF;
        char hex_char;
        if (nibble < 10)
            hex_char = '0' + nibble;
        else
            hex_char = 'A' + (nibble - 10);
        terminal_putchar(hex_char);
    }

    terminal_writestring(" from the stack\n");
}

void handle_clear() {
    terminal_initialize();
}

void handle_reboot() {
    terminal_writestring("Rebooting...\n");
    outb(0x64, 0xFE);
}

void handle_halt() {
    terminal_writestring("System halted\n");
    __asm__ volatile("hlt");
}

void handle_shutdown() {
    terminal_writestring("Shutting down...\n");

    outw(0xB004, 0x2000); /* ACPI shutdown */
    outw(0x604, 0x2000);  /* APM shutdown */

    terminal_writestring("Shutdown failed, halting CPU\n");
    __asm__ volatile("cli; hlt");
}

void handle_memstat() {
    mem_print_stats();
}

void handle_phystat() {
    phys_print_stats();
}

void handle_vmstat() {
    vm_print_stats();
}

void handle_heapdump() {
    mem_dump_heap();
}

void handle_pagedump() {
    mem_dump_page_directory(current_directory);
}

void handle_kmalloc(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: kmalloc requires a size\n");
        return;
    }

    uint32_t size = 0;
    int i = 0;

    while (arg[i] != '\0') {
        if (arg[i] >= '0' && arg[i] <= '9') {
            size = size * 10 + (arg[i] - '0');
        } else {
            terminal_writestring("Error: Invalid size\n");
            return;
        }
        i++;
    }

    void *ptr = kmalloc(size);
    if (ptr) {
        terminal_writestring("Allocated ");
        printnbr(size, 10);
        terminal_writestring(" bytes at 0x");
        printnbr((uint32_t)ptr, 16);
        terminal_writestring("\n");
    } else {
        terminal_writestring("Failed to allocate memory\n");
    }
}

void handle_kfree(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: kfree requires an address\n");
        return;
    }

    uint32_t addr = 0;
    int i = 0;

    if (arg[0] == '0' && (arg[1] == 'x' || arg[1] == 'X'))
        i = 2;

    while (arg[i] != '\0') {
        addr = addr * 16;
        if (arg[i] >= '0' && arg[i] <= '9')
            addr += arg[i] - '0';
        else if (arg[i] >= 'a' && arg[i] <= 'f')
            addr += arg[i] - 'a' + 10;
        else if (arg[i] >= 'A' && arg[i] <= 'F')
            addr += arg[i] - 'A' + 10;
        else {
            terminal_writestring("Error: Invalid hex address\n");
            return;
        }
        i++;
    }

    kfree((void*)addr);
    terminal_writestring("Freed memory at 0x");
    printnbr(addr, 16);
    terminal_writestring("\n");
}

void handle_vmalloc(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: vmalloc requires a size\n");
        return;
    }

    uint32_t size = 0;
    int i = 0;

    while (arg[i] != '\0') {
        if (arg[i] >= '0' && arg[i] <= '9') {
            size = size * 10 + (arg[i] - '0');
        } else {
            terminal_writestring("Error: Invalid size\n");
            return;
        }
        i++;
    }

    void *ptr = vmalloc(size);
    if (ptr) {
        terminal_writestring("Allocated ");
        printnbr(size, 10);
        terminal_writestring(" bytes at 0x");
        printnbr((uint32_t)ptr, 16);
        terminal_writestring("\n");
    } else {
        terminal_writestring("Failed to allocate virtual memory\n");
    }
}

void handle_vfree(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: vfree requires an address\n");
        return;
    }

    uint32_t addr = 0;
    int i = 0;

    if (arg[0] == '0' && (arg[1] == 'x' || arg[1] == 'X'))
        i = 2;

    while (arg[i] != '\0') {
        addr = addr * 16;
        if (arg[i] >= '0' && arg[i] <= '9')
            addr += arg[i] - '0';
        else if (arg[i] >= 'a' && arg[i] <= 'f')
            addr += arg[i] - 'a' + 10;
        else if (arg[i] >= 'A' && arg[i] <= 'F')
            addr += arg[i] - 'A' + 10;
        else {
            terminal_writestring("Error: Invalid hex address\n");
            return;
        }
        i++;
    }

    vfree((void*)addr);
    terminal_writestring("Freed virtual memory at 0x");
    printnbr(addr, 16);
    terminal_writestring("\n");
}

void handle_panic(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: panic requires a level (0-4)\n");
        return;
    }

    int level = arg[0] - '0';
    if (level < 0 || level > 4) {
        terminal_writestring("Error: panic level must be 0-4\n");
        return;
    }

    switch (level) {
        case 0:
            INFO("Test info message from shell");
            break;
        case 1:
            WARN("Test warning message from shell");
            break;
        case 2:
            ERROR("Test error message from shell");
            break;
        case 3:
            CRITICAL("Test critical message from shell");
            break;
        case 4:
            PANIC("Test fatal panic from shell - system will halt!");
            break;
    }
}

void handle_unknown(const char* command) {
    terminal_writestring("Unknown command: ");
    terminal_writestring(command);
    terminal_writestring("\n");
}

command_type_t get_command_type(const char* command) {
	command_type_t cmd_type;

	cmd_type = CMD_UNKNOWN;
    if (strcmp(command, "help") == 0)
		cmd_type = CMD_HELP;
    else if (strcmp(command, "stack") == 0)
		cmd_type = CMD_STACK;
    else if (strcmp(command, "push") == 0)
		cmd_type = CMD_PUSH;
    else if (strcmp(command, "pop") == 0)
		cmd_type = CMD_POP;
    else if (strcmp(command, "clear") == 0)
		cmd_type = CMD_CLEAR;
    else if (strcmp(command, "reboot") == 0)
		cmd_type = CMD_REBOOT;
    else if (strcmp(command, "halt") == 0)
		cmd_type = CMD_HALT;
    else if (strcmp(command, "shutdown") == 0)
		cmd_type = CMD_SHUTDOWN;
    else if (strcmp(command, "memstat") == 0)
		cmd_type = CMD_MEMSTAT;
    else if (strcmp(command, "phystat") == 0)
		cmd_type = CMD_PHYSTAT;
    else if (strcmp(command, "vmstat") == 0)
		cmd_type = CMD_VMSTAT;
    else if (strcmp(command, "heapdump") == 0)
		cmd_type = CMD_HEAPDUMP;
    else if (strcmp(command, "pagedump") == 0)
		cmd_type = CMD_PAGEDUMP;
    else if (strcmp(command, "kmalloc") == 0)
		cmd_type = CMD_KMALLOC;
    else if (strcmp(command, "kfree") == 0)
		cmd_type = CMD_KFREE;
    else if (strcmp(command, "vmalloc") == 0)
		cmd_type = CMD_VMALLOC;
    else if (strcmp(command, "vfree") == 0)
		cmd_type = CMD_VFREE;
    else if (strcmp(command, "panic") == 0)
		cmd_type = CMD_PANIC;
	return cmd_type;
}

void shell_process_command(const char* cmd) {
    char command[32] = {0};
    char arg[32] = {0};
    int i = 0, j = 0;

    while (cmd[i] && cmd[i] != ' ' && i < 31) {
        command[i] = cmd[i];
        i++;
    }
    command[i] = '\0';

    while (cmd[i] && cmd[i] == ' ')
        i++;

    while (cmd[i] && j < 31) {
        arg[j] = cmd[i];
        i++;
        j++;
    }
    arg[j] = '\0';

    command_type_t cmd_type = get_command_type(command);

    switch (cmd_type) {
        case CMD_HELP:
            handle_help();
            break;
        case CMD_STACK:
            handle_stack();
            break;
        case CMD_PUSH:
            handle_push(arg);
            break;
        case CMD_POP:
            handle_pop();
            break;
        case CMD_CLEAR:
            handle_clear();
            break;
        case CMD_REBOOT:
            handle_reboot();
            break;
        case CMD_HALT:
            handle_halt();
            break;
        case CMD_SHUTDOWN:
            handle_shutdown();
            break;
        case CMD_MEMSTAT:
            handle_memstat();
            break;
        case CMD_PHYSTAT:
            handle_phystat();
            break;
        case CMD_VMSTAT:
            handle_vmstat();
            break;
        case CMD_HEAPDUMP:
            handle_heapdump();
            break;
        case CMD_PAGEDUMP:
            handle_pagedump();
            break;
        case CMD_KMALLOC:
            handle_kmalloc(arg);
            break;
        case CMD_KFREE:
            handle_kfree(arg);
            break;
        case CMD_VMALLOC:
            handle_vmalloc(arg);
            break;
        case CMD_VFREE:
            handle_vfree(arg);
            break;
        case CMD_PANIC:
            handle_panic(arg);
            break;
        case CMD_UNKNOWN:
            if (command[0] != '\0')
                handle_unknown(command);
            break;
    }

    terminal_writestring("> ");
}

void shell_handle_input(char c) {
    if (c == '\n') {
        terminal_putchar('\n');
        command_buffer[buffer_pos] = '\0';
        shell_process_command(command_buffer);
        buffer_pos = 0;
    } else if (c == '\b' && buffer_pos > 0) {
        buffer_pos--;
        terminal_putchar('\b');
        terminal_putchar(' ');
        terminal_putchar('\b');
    } else if (c >= ' ' && c <= '~' && buffer_pos < COMMAND_BUFFER_SIZE - 1) {
        command_buffer[buffer_pos++] = c;
        terminal_putchar(c);
    }
}

int strcmp(const char* s1, const char* s2) {
    while (*s1 && (*s1 == *s2)) {
        s1++;
        s2++;
    }
    return *(const unsigned char*)s1 - *(const unsigned char*)s2;
}
