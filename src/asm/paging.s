BITS 32

global paging_load_directory
global paging_enable_paging
global paging_disable_paging
global paging_invalidate_page

; Load a page directory into CR3
; void paging_load_directory(uint32_t page_dir_phys_addr)
paging_load_directory:
    push ebp
    mov ebp, esp
    
    mov eax, [ebp + 8]      ; Get page directory physical address
    mov cr3, eax            ; Load into CR3
    
    pop ebp
    ret

; Enable paging by setting the PG bit in CR0
; void paging_enable_paging(void)
paging_enable_paging:
    push ebp
    mov ebp, esp
    
    mov eax, cr0
    or eax, 0x80000000      ; Set PG bit (bit 31)
    mov cr0, eax
    
    pop ebp
    ret

; Disable paging by clearing the PG bit in CR0
; void paging_disable_paging(void)
paging_disable_paging:
    push ebp
    mov ebp, esp
    
    mov eax, cr0
    and eax, 0x7FFFFFFF     ; Clear PG bit (bit 31)
    mov cr0, eax
    
    pop ebp
    ret

; Invalidate a single page in the TLB
; void paging_invalidate_page(uint32_t virtual_addr)
paging_invalidate_page:
    push ebp
    mov ebp, esp
    
    mov eax, [ebp + 8]      ; Get virtual address
    invlpg [eax]            ; Invalidate page in TLB
    
    pop ebp
    ret

; Get the current page directory address from CR3
; uint32_t paging_get_directory(void)
global paging_get_directory
paging_get_directory:
    mov eax, cr3
    ret

; Flush the entire TLB by reloading CR3
; void paging_flush_tlb(void)
global paging_flush_tlb
paging_flush_tlb:
    mov eax, cr3
    mov cr3, eax
    ret
