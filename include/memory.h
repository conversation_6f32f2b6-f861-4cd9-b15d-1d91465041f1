/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   memory.h                                           :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/02 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/02 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef MEMORY_H
# define MEMORY_H

#include <stdint.h>
#include <stddef.h>

/* ──────────── Memory Constants ──────────── */
#define PAGE_SIZE           4096        /* 4KB pages */
#define PAGE_ENTRIES        1024        /* Entries per page table/directory */
#define PAGE_ALIGN(addr)    ((addr) & ~(PAGE_SIZE - 1))
#define PAGE_OFFSET(addr)   ((addr) & (PAGE_SIZE - 1))
#define PAGE_INDEX(addr)    (((addr) >> 12) & 0x3FF)
#define PAGE_DIR_INDEX(addr) (((addr) >> 22) & 0x3FF)

/* Memory layout constants */
#define KERNEL_START        0x100000    /* 1MB - where kernel is loaded */
#define KERNEL_HEAP_START   0x400000    /* 4MB - start of kernel heap */
#define KERNEL_HEAP_SIZE    0x400000    /* 4MB - kernel heap size */
#define USER_SPACE_START    0x40000000  /* 1GB - start of user space */
#define USER_SPACE_END      0xC0000000  /* 3GB - end of user space */

/* Page flags */
#define PAGE_PRESENT        0x001       /* Page is present in memory */
#define PAGE_WRITABLE       0x002       /* Page is writable */
#define PAGE_USER           0x004       /* Page is accessible by user */
#define PAGE_WRITE_THROUGH  0x008       /* Write-through caching */
#define PAGE_CACHE_DISABLE  0x010       /* Cache disabled */
#define PAGE_ACCESSED       0x020       /* Page was accessed */
#define PAGE_DIRTY          0x040       /* Page was written to */
#define PAGE_SIZE_4MB       0x080       /* 4MB page (only in page directory) */
#define PAGE_GLOBAL         0x100       /* Global page */

/* Memory allocation flags */
#define KMALLOC_ZERO        0x001       /* Zero the allocated memory */
#define KMALLOC_ATOMIC      0x002       /* Atomic allocation (no sleep) */

/* ──────────── Data Structures ──────────── */

/* Page directory entry */
typedef struct s_page_dir_entry {
    uint32_t present    : 1;    /* Page present in memory */
    uint32_t writable   : 1;    /* Read/write permission */
    uint32_t user       : 1;    /* User/supervisor */
    uint32_t pwt        : 1;    /* Page write through */
    uint32_t pcd        : 1;    /* Page cache disable */
    uint32_t accessed   : 1;    /* Has been accessed */
    uint32_t reserved   : 1;    /* Reserved (set to 0) */
    uint32_t page_size  : 1;    /* Page size (0 = 4KB, 1 = 4MB) */
    uint32_t ignored    : 4;    /* Ignored */
    uint32_t frame      : 20;   /* Frame address (bits 12-31) */
} __attribute__((packed)) t_page_dir_entry;

/* Page table entry */
typedef struct s_page_table_entry {
    uint32_t present    : 1;    /* Page present in memory */
    uint32_t writable   : 1;    /* Read/write permission */
    uint32_t user       : 1;    /* User/supervisor */
    uint32_t pwt        : 1;    /* Page write through */
    uint32_t pcd        : 1;    /* Page cache disable */
    uint32_t accessed   : 1;    /* Has been accessed */
    uint32_t dirty      : 1;    /* Has been written to */
    uint32_t pat        : 1;    /* Page attribute table */
    uint32_t global     : 1;    /* Global page */
    uint32_t ignored    : 3;    /* Ignored */
    uint32_t frame      : 20;   /* Frame address (bits 12-31) */
} __attribute__((packed)) t_page_table_entry;

/* Page directory */
typedef struct s_page_directory {
    t_page_dir_entry entries[PAGE_ENTRIES];
    uint32_t physical_addr;     /* Physical address of this directory */
} __attribute__((aligned(PAGE_SIZE))) t_page_directory;

/* Page table */
typedef struct s_page_table {
    t_page_table_entry entries[PAGE_ENTRIES];
} __attribute__((aligned(PAGE_SIZE))) t_page_table;

/* Physical memory bitmap */
typedef struct s_phys_mem_manager {
    uint32_t *bitmap;           /* Bitmap of used/free frames */
    uint32_t total_frames;      /* Total number of frames */
    uint32_t used_frames;       /* Number of used frames */
    uint32_t free_frames;       /* Number of free frames */
    uint32_t first_free_frame;  /* Hint for first free frame */
} t_phys_mem_manager;

/* Memory block header for heap allocation */
typedef struct s_mem_block {
    uint32_t size;              /* Size of the block */
    uint32_t magic;             /* Magic number for validation */
    struct s_mem_block *next;   /* Next block in list */
    struct s_mem_block *prev;   /* Previous block in list */
    uint8_t free;               /* Is this block free? */
} __attribute__((packed)) t_mem_block;

/* Kernel heap structure */
typedef struct s_kernel_heap {
    t_mem_block *first_block;   /* First block in heap */
    uint32_t start_addr;        /* Start address of heap */
    uint32_t end_addr;          /* End address of heap */
    uint32_t size;              /* Total heap size */
    uint32_t used;              /* Used memory */
    uint32_t free;              /* Free memory */
} t_kernel_heap;

/* Memory statistics */
typedef struct s_mem_stats {
    uint32_t total_memory;      /* Total physical memory */
    uint32_t used_memory;       /* Used physical memory */
    uint32_t free_memory;       /* Free physical memory */
    uint32_t kernel_memory;     /* Memory used by kernel */
    uint32_t user_memory;       /* Memory used by user processes */
    uint32_t cached_memory;     /* Cached memory */
} t_mem_stats;

/* Panic levels */
typedef enum e_panic_level {
    PANIC_INFO,                 /* Informational - continue */
    PANIC_WARNING,              /* Warning - continue with caution */
    PANIC_ERROR,                /* Error - try to recover */
    PANIC_CRITICAL,             /* Critical - system unstable */
    PANIC_FATAL                 /* Fatal - halt system */
} t_panic_level;

/* Panic statistics structure */
typedef struct s_panic_statistics {
    uint32_t total_panics;
    uint32_t info_count;
    uint32_t warning_count;
    uint32_t error_count;
    uint32_t critical_count;
    uint32_t fatal_count;
} t_panic_statistics;

/* ──────────── Function Declarations ──────────── */

/* Physical Memory Management */
void        phys_mem_init(uint32_t mem_size);
uint32_t    phys_alloc_frame(void);
void        phys_free_frame(uint32_t frame_addr);
void        phys_set_frame(uint32_t frame_addr);
void        phys_clear_frame(uint32_t frame_addr);
int         phys_test_frame(uint32_t frame_addr);
uint32_t    phys_get_free_frames(void);
uint32_t    phys_get_total_memory(void);
uint32_t    phys_get_used_memory(void);
uint32_t    phys_get_free_memory(void);
void        phys_print_stats(void);

/* Paging and Virtual Memory */
void        paging_init(void);
void        paging_enable(void);
void        paging_disable(void);
t_page_directory* paging_create_directory(void);
void        paging_destroy_directory(t_page_directory *dir);
void        paging_switch_directory(t_page_directory *dir);
int         paging_map_page(uint32_t virt_addr, uint32_t phys_addr, uint32_t flags);
void        paging_unmap_page(uint32_t virt_addr);
uint32_t    paging_get_physical_addr(uint32_t virt_addr);
t_page_table* paging_get_page_table(uint32_t virt_addr, int create);

/* Kernel Memory Allocation */
void        *kmalloc(size_t size);
void        *kmalloc_a(size_t size);        /* Aligned allocation */
void        *kmalloc_p(size_t size, uint32_t *phys_addr);  /* Get physical addr */
void        *kmalloc_ap(size_t size, uint32_t *phys_addr); /* Aligned + physical */
void        kfree(void *ptr);
size_t      ksize(void *ptr);
void        *kbrk(intptr_t increment);
void        *kcalloc(size_t num, size_t size);
void        *krealloc(void *ptr, size_t size);

/* Virtual Memory Allocation */
void        *vmalloc(size_t size);
void        vfree(void *ptr);
size_t      vsize(void *ptr);
void        *vbrk(intptr_t increment);
void        vm_get_stats(uint32_t *total_allocated, uint32_t *num_areas);
void        vm_print_stats(void);
void        vm_dump_areas(void);

/* Memory Management Initialization */
void        memory_init(uint32_t mem_size);
void        heap_init(void);

/* Memory Statistics and Debugging */
void        mem_get_stats(t_mem_stats *stats);
void        mem_print_stats(void);
void        mem_dump_page_directory(t_page_directory *dir);
void        mem_dump_heap(void);

/* Kernel Panic System */
void        kernel_panic(t_panic_level level, const char *message);
void        kernel_panic_at(t_panic_level level, const char *file, int line, const char *message);
void        get_panic_stats(t_panic_statistics *stats);
void        print_stack_trace(void);
void        print_memory_info(void);

/* Memory Statistics and Debugging */
void        mem_get_stats(t_mem_stats *stats);
void        mem_print_stats(void);
void        mem_dump_page_directory(t_page_directory *dir);
void        mem_dump_heap(void);

/* Utility Functions */
void        *memset(void *ptr, int value, size_t size);
void        *memcpy(void *dest, const void *src, size_t size);
int         memcmp(const void *ptr1, const void *ptr2, size_t size);

/* Global Variables */
extern t_page_directory *kernel_directory;
extern t_page_directory *current_directory;
extern t_phys_mem_manager phys_mem_mgr;
extern t_kernel_heap kernel_heap;

/* Macros for convenience */
#define PANIC(msg) kernel_panic_at(PANIC_FATAL, __FILE__, __LINE__, msg)
#define CRITICAL(msg) kernel_panic_at(PANIC_CRITICAL, __FILE__, __LINE__, msg)
#define ERROR(msg) kernel_panic_at(PANIC_ERROR, __FILE__, __LINE__, msg)
#define WARN(msg) kernel_panic_at(PANIC_WARNING, __FILE__, __LINE__, msg)
#define INFO(msg) kernel_panic_at(PANIC_INFO, __FILE__, __LINE__, msg)

#endif /* MEMORY_H */
